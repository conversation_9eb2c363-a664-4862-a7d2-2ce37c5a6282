<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Capture and Utilisation | MSN / UM6P</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Modern Research Page Styles */
        .research-hero {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .research-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(231,76,60,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 900px;
            margin: 0 auto;
        }

        .research-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--foreground);
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.25rem;
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--foreground-light);
            font-weight: 500;
        }

        /* Modern Navigation Tabs */
        .research-navigation {
            background: white;
            border-bottom: 1px solid rgba(231, 76, 60, 0.1);
            position: sticky;
            top: 80px;
            z-index: 50;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            flex: 1;
            min-width: 150px;
            padding: 1.25rem 1.5rem;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            color: var(--foreground-light);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            white-space: nowrap;
        }

        .nav-tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .nav-tab:hover {
            color: var(--primary);
            background: rgba(231, 76, 60, 0.02);
        }

        .nav-tab.active {
            color: var(--primary);
            background: rgba(231, 76, 60, 0.05);
        }

        .nav-tab.active::after {
            transform: scaleX(1);
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--foreground-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Modern Card Grid */
        .modern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .card-description {
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .card-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.875rem;
            color: var(--primary);
            font-weight: 600;
        }

        /* Team Grid */
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .team-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(41, 128, 185, 0.08);
            border: 2px solid rgba(41, 128, 185, 0.1);
            transition: all 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(41, 128, 185, 0.15);
        }

        .team-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            border: 4px solid rgba(41, 128, 185, 0.1);
            overflow: hidden;
        }

        .team-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .team-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 0.5rem;
        }

        .team-role {
            color: var(--secondary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .modern-grid {
                grid-template-columns: 1fr;
            }

            .nav-tab {
                min-width: 120px;
                padding: 1rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="index.html">
                    <img src="assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="index.html" class="nav-link">Home</a>
                <a href="um6p.html" class="nav-link">UM6P</a>
        <div class="dropdown">
          <a href="research.html" class="nav-link active">Research Clusters</a>
          <div class="dropdown-content">
            <div class="dropdown-section">
              <h4>Energy Transition</h4>
              <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
              <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
              <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
              <a href="research-solar.html">Solar Energy Materials</a>
            </div>
            <div class="dropdown-section">
              <h4>Smart & Functional Materials</h4>
              <a href="research-plasma.html">Plasma & Coatings Science</a>
              <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
              <a href="research-polymers.html">Functional Polymers</a>
              <a href="research-metallurgy.html">Metallurgy</a>
            </div>
            <div class="dropdown-section">
              <h4>Circular Materials</h4>
              <a href="research-recycling.html">Sustainable Materials & Recycling</a>
              <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
            </div>
          </div>
        </div>
        <a href="publications.html" class="nav-link">Publications</a>
        <a href="education.html" class="nav-link">Education</a>
      </nav>
      <button class="mobile-menu-button" id="mobileMenuButton">...</button>
    </div>
    <div class="mobile-menu" id="mobileMenu">
      <a href="index.html" class="mobile-nav-link">Home</a>
      <a href="about.html" class="mobile-nav-link">About</a>
      <a href="research.html" class="mobile-nav-link active">Research Clusters</a>
      <div class="mobile-submenu">
        <div class="mobile-submenu-section">
          <h4>Energy Transition</h4>
          <a href="research-energy-storage.html" class="mobile-submenu-link">Electrochemical Energy Storage</a>
          <a href="research-hydrogen.html" class="mobile-submenu-link">Hydrogen Production & Utilization</a>
          <a href="research-gas-capture.html" class="mobile-submenu-link">Gas Capture and Utilisation</a>
          <a href="research-solar.html" class="mobile-submenu-link">Solar Energy Materials</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Smart & Functional Materials</h4>
          <a href="research-plasma.html" class="mobile-submenu-link">Plasma & Coatings Science</a>
          <a href="research-biomass.html" class="mobile-submenu-link">Biomass Valorization, Bio-Polymers & Composites</a>
          <a href="research-polymers.html" class="mobile-submenu-link">Functional Polymers</a>
          <a href="research-metallurgy.html" class="mobile-submenu-link">Metallurgy</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Circular Materials</h4>
          <a href="research-recycling.html" class="mobile-submenu-link">Sustainable Materials & Recycling</a>
          <a href="research-value-products.html" class="mobile-submenu-link">Recycling and Extraction of Value Products</a>
        </div>
      </div>
      <a href="publications.html" class="mobile-nav-link">Publications</a>
      <a href="education.html" class="mobile-nav-link">Education</a>
    </div>
  </header>

  <main>
    <section class="page-header">
      <h1>Gas Capture and Utilisation</h1>
      <p class="page-description">Engineering innovative materials and processes for carbon capture, air purification, and circular gas-based applications.</p>
    </section>

    <section class="research-tabs container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="topics">Topics</button>
        <button class="tab-button" data-tab="modules">Teaching Modules</button>
        <button class="tab-button" data-tab="projects">Projects</button>
        <button class="tab-button" data-tab="team">Team</button>
      </div>

      <div class="tab-content active" id="topics-content">
        <h2>Research Topics</h2>
        <div class="simple-grid">
          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-cloud"></i>
            </div>
            <div class="simple-content">
              <h3>CO2 capture technologies</h3>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-industry"></i>
            </div>
            <div class="simple-content">
              <h3>Industrial gas separation</h3>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-atom"></i>
            </div>
            <div class="simple-content">
              <h3>Advanced materials for gas adsorption</h3>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-flask"></i>
            </div>
            <div class="simple-content">
              <h3>CO2 conversion to value-added products</h3>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="simple-content">
              <h3>Process modeling and optimization</h3>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="modules-content">
        <h2>Teaching Modules</h2>
        <div class="simple-grid">
          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-cloud-download-alt"></i>
            </div>
            <div class="simple-content">
              <h3>Carbon Capture Technologies</h3>
              <p>MEME Program</p>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-recycle"></i>
            </div>
            <div class="simple-content">
              <h3>CO2 Utilization</h3>
              <p>MEME Program</p>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-industry"></i>
            </div>
            <div class="simple-content">
              <h3>Industrial Gas Processing</h3>
              <p>Executive Master in Energy</p>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="projects-content">
        <h2>Current Projects</h2>
        <div class="simple-grid">
          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-microscope"></i>
            </div>
            <div class="simple-content">
              <h3>MOF Development for CO2 Capture</h3>
              <p>Novel materials for selective gas adsorption</p>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-industry"></i>
            </div>
            <div class="simple-content">
              <h3>Phosphate Industry Emissions</h3>
              <p>Reducing carbon footprint in phosphate processing</p>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-flask"></i>
            </div>
            <div class="simple-content">
              <h3>CO2 to Chemicals</h3>
              <p>Catalytic conversion of CO2 to value-added products</p>
            </div>
          </div>

          <div class="simple-card">
            <div class="simple-icon">
              <i class="fas fa-solar-panel"></i>
            </div>
            <div class="simple-content">
              <h3>Solar-Driven CO2 Conversion</h3>
              <p>Renewable energy integration with carbon capture</p>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="team-content">
        <h2>Research Team</h2>
        <div class="team-grid">
          <div class="team-member">
            <img src="assets/images/team/johan.jpg" alt="Johan Jacquemin" />
            <h3>Prof. Johan Jacquemin</h3>
            <p class="member-role">Research Lead</p>
          </div>
          <div class="team-member">
            <img src="images/team-placeholder.png" alt="Zineb Ouzrour" />
            <h3>Zineb Ouzrour</h3>
            <p class="member-role">PhD Candidate – Porous Liquids</p>
          </div>
          <div class="team-member">
            <img src="images/team-placeholder.png" alt="Hicham Akaya" />
            <h3>Hicham Akaya</h3>
            <p class="member-role">PhD Candidate – DAC Technology</p>
          </div>
          <div class="team-member">
            <img src="images/team-placeholder.png" alt="Ismail El Khiraoui" />
            <h3>Ismail El Khiraoui</h3>
            <p class="member-role">Research Associate</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <img src="assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
          <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
        </div>
        <div class="footer-col">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="research.html">Research Areas</a></li>
            <li><a href="publications.html">Publications</a></li>
            <li><a href="education.html">Education Programs</a></li>
            <li><a href="https://www.um6p.ma" target="_blank">UM6P</a></li>
            <li><a href="https://ensus.um6p.ma" target="_blank">ENSUS Chair</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h4>Connect With Us</h4>
          <div class="social-links">
            <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                <rect x="2" y="9" width="4" height="12"></rect>
                <circle cx="4" cy="4" r="2"></circle>
              </svg>
            </a>
            <a href="mailto:<EMAIL>" aria-label="Email">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
          <p class="contact-info">
            Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
            Phone: +212 (0) 5 25 07 3000
          </p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <script>
    document.addEventListener("DOMContentLoaded", () => {
      document.getElementById("currentYear").textContent = new Date().getFullYear();
      const buttons = document.querySelectorAll(".tab-button");
      const contents = document.querySelectorAll(".tab-content");
      buttons.forEach((btn) => {
        btn.addEventListener("click", () => {
          const tab = btn.dataset.tab;
          buttons.forEach((b) => b.classList.remove("active"));
          contents.forEach((c) => c.classList.remove("active"));
          btn.classList.add("active");
          document.getElementById(`${tab}-content`).classList.add("active");
        });
      });
    });
  </script>
</body>
</html>
