# Equipment Page Setup Guide

## 📋 How to Extract Images from Excel and Update the Equipment Page

### Step 1: Extract Images from Excel File
1. **Open your equipment.xlsx file**
2. **Right-click on each equipment image** in Excel
3. **Select "Save as Picture"** or "Copy Image"
4. **Save images** with descriptive names like:
   - `battery-cycler-biologic.jpg`
   - `xrd-rigaku.jpg`
   - `sem-jeol.jpg`
   - etc.

### Step 2: Create Equipment Images Folder
Create this folder structure in your project:
```
assets/
  images/
    equipment/
      battery-cycler-biologic.jpg
      xrd-rigaku.jpg
      sem-jeol.jpg
      furnace-carbolite.jpg
      ... (all your equipment images)
```

### Step 3: Equipment Card Template
For each piece of equipment, use this template:

```html
<div class="equipment-card" data-category="CATEGORY_NAME">
    <div class="equipment-image">
        <img src="assets/images/equipment/IMAGE_NAME.jpg" alt="EQUIPMENT_NAME" 
             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
        <div class="no-image" style="display: none;">
            <i class="fas fa-microscope"></i>
            <span>Image not found</span>
        </div>
    </div>
    <div class="equipment-content">
        <h3 class="equipment-title">EQUIPMENT_NAME</h3>
        <div class="equipment-info">
            <div class="equipment-detail">
                <span class="label">Brand:</span>
                <span class="value">BRAND_NAME</span>
            </div>
            <div class="equipment-detail">
                <span class="label">Model:</span>
                <span class="value">MODEL_NUMBER</span>
            </div>
            <div class="equipment-detail">
                <span class="label">Supplier:</span>
                <span class="value">SUPPLIER_NAME</span>
            </div>
        </div>
        <div class="location-badge">
            <i class="fas fa-map-marker-alt"></i>
            LOCATION/AFFECTATION
        </div>
    </div>
</div>
```

### Step 4: Category Names
Use these category names in the `data-category` attribute:
- `characterization` - For analytical instruments (XRD, FTIR, etc.)
- `synthesis` - For material synthesis equipment (furnaces, reactors)
- `electrochemical` - For battery testing and electrochemical equipment
- `thermal` - For thermal analysis equipment (DSC, TGA, etc.)
- `microscopy` - For microscopy and imaging equipment (SEM, TEM, etc.)

### Step 5: Excel Data Mapping
From your equipment.xlsx file, map the columns to the template:
- **Equipment Name** → `EQUIPMENT_NAME`
- **Marque (Brand)** → `BRAND_NAME`
- **Model** → `MODEL_NUMBER`
- **Fournisseur (Supplier)** → `SUPPLIER_NAME`
- **Affectation (Location)** → `LOCATION/AFFECTATION`
- **Image** → Save as file and use in `IMAGE_NAME.jpg`

### Step 6: Example Equipment Card with Real Image
```html
<div class="equipment-card" data-category="electrochemical">
    <div class="equipment-image">
        <img src="assets/images/equipment/biologic-vmp3.jpg" alt="BioLogic VMP3 Battery Cycler" 
             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
        <div class="no-image" style="display: none;">
            <i class="fas fa-battery-full"></i>
            <span>Image not found</span>
        </div>
    </div>
    <div class="equipment-content">
        <h3 class="equipment-title">VMP3 Potentiostat/Galvanostat</h3>
        <div class="equipment-info">
            <div class="equipment-detail">
                <span class="label">Brand:</span>
                <span class="value">BioLogic</span>
            </div>
            <div class="equipment-detail">
                <span class="label">Model:</span>
                <span class="value">VMP3</span>
            </div>
            <div class="equipment-detail">
                <span class="label">Supplier:</span>
                <span class="value">BioLogic Science Instruments</span>
            </div>
        </div>
        <div class="location-badge">
            <i class="fas fa-map-marker-alt"></i>
            Laboratory A - Room 101
        </div>
    </div>
</div>
```

### Step 7: Image Requirements
- **Format**: JPG, PNG, or WebP
- **Size**: Recommended 800x600 pixels or similar aspect ratio
- **Quality**: High quality for clear display
- **Naming**: Use descriptive names without spaces (use hyphens instead)

### Step 8: Testing
1. **Save all images** in the `assets/images/equipment/` folder
2. **Update the equipment.html** file with your equipment data
3. **Open the page** in a browser to verify images display correctly
4. **Test category filtering** to ensure equipment appears in correct categories

## 🔧 Need Help?
If you need assistance with:
- Extracting specific equipment data from your Excel file
- Creating the equipment cards
- Setting up the image folder structure
- Testing the category filtering

Just let me know and I can help you with the specific steps!
