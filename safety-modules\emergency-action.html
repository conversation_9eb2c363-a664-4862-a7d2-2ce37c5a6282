<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Action Plan | MSN Laboratory</title>
    <link rel="stylesheet" href="../styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Modern Research Page Styles */
        .research-hero {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .research-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(231,76,60,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .research-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(231, 76, 60, 0.1);
            color: var(--primary);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(231, 76, 60, 0.2);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--foreground);
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .hero-description {
            font-size: 1.25rem;
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--foreground-light);
            font-weight: 500;
        }

        /* Modern Navigation Tabs */
        .research-navigation {
            background: white;
            border-bottom: 1px solid rgba(231, 76, 60, 0.1);
            position: sticky;
            top: 80px;
            z-index: 50;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            padding: 1.5rem 2rem;
            font-weight: 600;
            color: var(--foreground-light);
            text-decoration: none;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 150px;
            text-align: center;
        }

        .nav-tab:hover {
            color: var(--primary);
            background: rgba(231, 76, 60, 0.05);
        }

        .nav-tab.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
            background: rgba(231, 76, 60, 0.05);
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .section-description {
            font-size: 1.125rem;
            color: var(--foreground-light);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* Modern Grid */
        .modern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .card-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .card-description {
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .card-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .card-list li {
            padding: 0.5rem 0;
            color: var(--foreground-light);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-list li::before {
            content: '✓';
            color: var(--primary);
            font-weight: bold;
            width: 20px;
            height: 20px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }

        /* Assessment Button */
        .btn-assessment {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .btn-assessment:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .modern-grid {
                grid-template-columns: 1fr;
            }

            .nav-tab {
                min-width: 120px;
                padding: 1rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html">
                    <img src="../assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="../index.html" class="nav-link">Home</a>
                <a href="../um6p.html" class="nav-link">UM6P</a>
                <div class="dropdown">
                    <a href="../research.html" class="nav-link">Research Clusters</a>
                    <div class="dropdown-content">
                        <div class="dropdown-section">
                            <h4>Energy Transition</h4>
                            <a href="../research-energy-storage.html">Electrochemical Energy Storage</a>
                            <a href="../research-hydrogen.html">Hydrogen Production & Utilization</a>
                            <a href="../research-gas-capture.html">Gas Capture and Utilisation</a>
                            <a href="../research-solar.html">Solar Energy Materials</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Smart & Functional Materials</h4>
                            <a href="../research-plasma.html">Plasma & Coatings Science</a>
                            <a href="../research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                            <a href="../research-polymers.html">Functional Polymers</a>
                            <a href="../research-metallurgy.html">Metallurgy</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Circular Materials</h4>
                            <a href="../research-recycling.html">Sustainable Materials & Recycling</a>
                            <a href="../research-value-products.html">Recycling and Extraction of Value Products</a>
                        </div>
                    </div>
                </div>
                <a href="../publications.html" class="nav-link">Publications</a>
                <a href="../education.html" class="nav-link">Education</a>
                <a href="../laboratory.html" class="nav-link active">Laboratory</a>
            </nav>
            <button class="mobile-menu-button" id="mobileMenuButton">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link">Home</a>
            <a href="../um6p.html" class="mobile-nav-link">UM6P</a>
            <div class="mobile-dropdown">
                <a href="../research.html" class="mobile-nav-link">Research Clusters</a>
                <div class="mobile-submenu">
                    <div class="mobile-submenu-section">
                        <h4>Energy Transition</h4>
                        <a href="../research-energy-storage.html">Electrochemical Energy Storage</a>
                        <a href="../research-hydrogen.html">Hydrogen Production & Utilization</a>
                        <a href="../research-gas-capture.html">Gas Capture and Utilisation</a>
                        <a href="../research-solar.html">Solar Energy Materials</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Smart & Functional Materials</h4>
                        <a href="../research-plasma.html">Plasma & Coatings Science</a>
                        <a href="../research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                        <a href="../research-polymers.html">Functional Polymers</a>
                        <a href="../research-metallurgy.html">Metallurgy</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Circular Materials</h4>
                        <a href="../research-recycling.html">Sustainable Materials & Recycling</a>
                        <a href="../research-value-products.html">Recycling and Extraction of Value Products</a>
                    </div>
                </div>
            </div>
            <a href="../publications.html" class="mobile-nav-link">Publications</a>
            <a href="../education.html" class="mobile-nav-link">Education</a>
            <a href="../laboratory.html" class="mobile-nav-link active">Laboratory</a>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="research-hero">
            <div class="container">
                <div class="hero-content">
                    <div class="research-badge">
                        <i class="fas fa-exclamation-triangle"></i>
                        Safety Training Module
                    </div>
                    <h1 class="hero-title">Emergency Action Plan</h1>
                    <p class="hero-description">
                        Comprehensive emergency response procedures and protocols for various laboratory emergencies.
                        Essential training for all laboratory personnel.
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number">2</span>
                            <span class="stat-label">Hours Duration</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">4</span>
                            <span class="stat-label">Key Areas</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Required</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Section -->
        <section class="content-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Training Content</h2>
                    <p class="section-description">
                        Master essential emergency response skills and procedures to ensure laboratory safety.
                    </p>
                </div>

                <div class="modern-grid">
                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3 class="card-title">Chemical Emergencies</h3>
                        <p class="card-description">
                            Learn proper response procedures for chemical spills, exposures, and containment.
                        </p>
                        <ul class="card-list">
                            <li>Spill response procedures</li>
                            <li>Chemical exposure treatment</li>
                            <li>Containment methods</li>
                            <li>Decontamination protocols</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h3 class="card-title">Medical Emergencies</h3>
                        <p class="card-description">
                            Essential first aid procedures and emergency medical response protocols.
                        </p>
                        <ul class="card-list">
                            <li>First aid procedures</li>
                            <li>Emergency contacts</li>
                            <li>Injury reporting</li>
                            <li>Medical facility locations</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-running"></i>
                        </div>
                        <h3 class="card-title">Evacuation Procedures</h3>
                        <p class="card-description">
                            Critical evacuation protocols and emergency exit procedures for laboratory safety.
                        </p>
                        <ul class="card-list">
                            <li>Emergency exit routes</li>
                            <li>Assembly points</li>
                            <li>Headcount procedures</li>
                            <li>Re-entry protocols</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <h3 class="card-title">Learning Objectives</h3>
                        <p class="card-description">
                            Key competencies you'll develop through this emergency action training module.
                        </p>
                        <ul class="card-list">
                            <li>Recognize emergency situations</li>
                            <li>Execute emergency procedures</li>
                            <li>Use emergency equipment</li>
                            <li>Coordinate with emergency responders</li>
                        </ul>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 4rem;">
                    <button class="btn-assessment">
                        <i class="fas fa-play"></i>
                        Start Assessment
                    </button>
                    <p style="margin-top: 1rem; color: var(--foreground-light);">
                        Complete emergency scenario simulations, equipment demonstrations, and evacuation drills
                    </p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <img src="../assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
                    <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
                </div>
                <div class="footer-col">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../research.html">Research Areas</a></li>
                        <li><a href="../publications.html">Publications</a></li>
                        <li><a href="../education.html">Education Programs</a></li>
                        <li><a href="../um6p.html">About UM6P</a></li>
                        <li><a href="../laboratory.html">Laboratory</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="#" aria-label="LinkedIn">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Email">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </a>
                    </div>
                    <p class="contact-info">
                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        Phone: +212 (0) 5 25 07 3000
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../scripts.js"></script>
    <script>
        // Set current year
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>