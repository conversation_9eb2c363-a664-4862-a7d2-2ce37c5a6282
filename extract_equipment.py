#!/usr/bin/env python3
"""
Equipment Data Extractor
Extracts equipment information from Excel file and generates HTML cards
"""

import pandas as pd
import json
import sys

def extract_equipment_data(excel_file):
    """Extract equipment data from Excel file"""
    try:
        # Read the Excel file
        df = pd.read_excel(excel_file)
        
        # Print column names to understand the structure
        print("Column names found:")
        for i, col in enumerate(df.columns):
            print(f"{i+1}. {col}")
        
        print(f"\nTotal rows: {len(df)}")
        print(f"Total columns: {len(df.columns)}")
        
        # Show first few rows
        print("\nFirst 5 rows:")
        print(df.head().to_string())
        
        # Save to JSON for easier processing
        equipment_data = df.to_dict('records')
        
        with open('equipment_data.json', 'w', encoding='utf-8') as f:
            json.dump(equipment_data, f, indent=2, ensure_ascii=False)
        
        print(f"\nData saved to equipment_data.json")
        return equipment_data
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def categorize_equipment(equipment_name):
    """Categorize equipment based on name"""
    name_lower = equipment_name.lower()
    
    if any(word in name_lower for word in ['microscope', 'sem', 'tem', 'afm', 'optical']):
        return 'microscopy'
    elif any(word in name_lower for word in ['furnace', 'oven', 'reactor', 'synthesis', 'cvd', 'pvd']):
        return 'synthesis'
    elif any(word in name_lower for word in ['battery', 'electrochemical', 'potentiostat', 'cycler', 'impedance']):
        return 'electrochemical'
    elif any(word in name_lower for word in ['dsc', 'tga', 'thermal', 'calorimeter', 'temperature']):
        return 'thermal'
    elif any(word in name_lower for word in ['xrd', 'ftir', 'raman', 'spectro', 'diffract', 'uv-vis', 'nmr']):
        return 'characterization'
    else:
        return 'characterization'  # default category

if __name__ == "__main__":
    excel_file = "equipemet.xlsx"
    equipment_data = extract_equipment_data(excel_file)
    
    if equipment_data:
        print(f"\nSuccessfully extracted {len(equipment_data)} equipment items")
