<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hazard-Specific Safety Training | MSN Laboratory</title>
    <link rel="stylesheet" href="../styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Modern Research Page Styles */
        .research-hero {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .research-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(231,76,60,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 900px;
            margin: 0 auto;
        }

        .research-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--foreground);
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.25rem;
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--foreground-light);
            font-weight: 500;
        }

        /* Breadcrumb */
        .breadcrumb {
            background: rgba(231, 76, 60, 0.05);
            padding: 1rem 0;
            border-bottom: 1px solid rgba(231, 76, 60, 0.1);
        }

        .breadcrumb-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--foreground-light);
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--foreground-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Modern Card Grid */
        .modern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .card-description {
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .card-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .card-features li {
            color: var(--foreground-light);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .card-features li::before {
            content: '✓';
            color: var(--primary);
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        /* Hazard Cards */
        .hazard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .hazard-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .hazard-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
        }

        .hazard-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1.5rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        /* Assessment Section */
        .assessment-section {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            border-radius: 20px;
            padding: 3rem;
            margin: 3rem 0;
            border: 2px solid rgba(231, 76, 60, 0.1);
            text-align: center;
        }

        .assessment-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .assessment-description {
            font-size: 1.125rem;
            color: var(--foreground-light);
            margin-bottom: 2rem;
        }

        .btn-assessment {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-assessment:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        /* Progress Indicator */
        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(231, 76, 60, 0.1);
            z-index: 1000;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .modern-grid,
            .hazard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- Header Navigation -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html">
                    <img src="../assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="../index.html" class="nav-link">Home</a>
                <a href="../um6p.html" class="nav-link">UM6P</a>
                <div class="dropdown">
                    <a href="../research.html" class="nav-link">Research Clusters</a>
                    <div class="dropdown-content">
                        <div class="dropdown-section">
                            <h4>Energy Transition</h4>
                            <a href="../research-energy-storage.html">Electrochemical Energy Storage</a>
                            <a href="../research-hydrogen.html">Hydrogen Production & Utilization</a>
                            <a href="../research-gas-capture.html">Gas Capture and Utilisation</a>
                            <a href="../research-solar.html">Solar Energy Materials</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Smart & Functional Materials</h4>
                            <a href="../research-plasma.html">Plasma & Coatings Science</a>
                            <a href="../research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                            <a href="../research-polymers.html">Functional Polymers</a>
                            <a href="../research-metallurgy.html">Metallurgy</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Circular Materials</h4>
                            <a href="../research-recycling.html">Sustainable Materials & Recycling</a>
                            <a href="../research-value-products.html">Recycling and Extraction of Value Products</a>
                        </div>
                    </div>
                </div>
                <a href="../publications.html" class="nav-link">Publications</a>
                <a href="../education.html" class="nav-link">Education</a>
                <a href="../laboratory.html" class="nav-link active">Laboratory</a>
            </nav>
            <button class="mobile-menu-button" id="mobileMenuButton">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link">Home</a>
            <a href="../um6p.html" class="mobile-nav-link">UM6P</a>
            <div class="mobile-dropdown">
                <a href="../research.html" class="mobile-nav-link">Research Clusters</a>
                <div class="mobile-submenu">
                    <div class="mobile-submenu-section">
                        <h4>Energy Transition</h4>
                        <a href="../research-energy-storage.html">Electrochemical Energy Storage</a>
                        <a href="../research-hydrogen.html">Hydrogen Production & Utilization</a>
                        <a href="../research-gas-capture.html">Gas Capture and Utilisation</a>
                        <a href="../research-solar.html">Solar Energy Materials</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Smart & Functional Materials</h4>
                        <a href="../research-plasma.html">Plasma & Coatings Science</a>
                        <a href="../research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                        <a href="../research-polymers.html">Functional Polymers</a>
                        <a href="../research-metallurgy.html">Metallurgy</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Circular Materials</h4>
                        <a href="../research-recycling.html">Sustainable Materials & Recycling</a>
                        <a href="../research-value-products.html">Recycling and Extraction of Value Products</a>
                    </div>
                </div>
            </div>
            <a href="../publications.html" class="mobile-nav-link">Publications</a>
            <a href="../education.html" class="mobile-nav-link">Education</a>
            <a href="../laboratory.html" class="mobile-nav-link active">Laboratory</a>
        </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <section class="breadcrumb-section">
        <div class="container">
            <nav class="breadcrumb-nav">
                <a href="../index.html" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    Home
                </a>
                <span class="breadcrumb-separator">/</span>
                <a href="../laboratory.html" class="breadcrumb-link">Laboratory</a>
                <span class="breadcrumb-separator">/</span>
                <a href="../laboratory.html#safety" class="breadcrumb-link">Safety Training</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Hazard-Specific Safety Training</span>
            </nav>
        </div>
    </section>

    <!-- Hero Section -->
    <section class="research-hero">
        <div class="container">
            <div class="hero-content">
                <div class="research-badge">
                    <i class="fas fa-radiation"></i>
                    Safety Training Module
                </div>
                <h1 class="hero-title">Hazard-Specific Safety Training</h1>
                <p class="hero-description">
                    Specialized training covering specific hazards encountered in MSN laboratories and their control measures.
                </p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">3</span>
                        <span class="stat-label">Hours</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <span class="stat-label">Learning Objectives</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <span class="stat-label">Hazard Types</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">3</span>
                        <span class="stat-label">Assessment Components</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main>
        <!-- Learning Objectives Section -->
        <section class="content-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Learning Objectives</h2>
                    <p class="section-subtitle">
                        Master specialized safety procedures for advanced laboratory equipment and hazardous materials.
                    </p>
                </div>

                <div class="modern-grid">
                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="card-title">Handle Specialized Equipment</h3>
                        <p class="card-description">
                            Safely operate and maintain specialized laboratory equipment.
                        </p>
                        <ul class="card-features">
                            <li>Equipment operation procedures</li>
                            <li>Maintenance protocols</li>
                            <li>Safety interlocks and controls</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3 class="card-title">Manage Hazardous Materials</h3>
                        <p class="card-description">
                            Proper handling, storage, and disposal of specialized hazardous materials.
                        </p>
                        <ul class="card-features">
                            <li>Material identification</li>
                            <li>Safe handling techniques</li>
                            <li>Storage requirements</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3 class="card-title">Implement Safety Protocols</h3>
                        <p class="card-description">
                            Apply specific safety protocols for specialized laboratory operations.
                        </p>
                        <ul class="card-features">
                            <li>Protocol development</li>
                            <li>Risk assessment</li>
                            <li>Control measures</li>
                        </ul>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-ambulance"></i>
                        </div>
                        <h3 class="card-title">Respond to Specialized Emergencies</h3>
                        <p class="card-description">
                            Emergency response procedures for specialized hazards and equipment.
                        </p>
                        <ul class="card-features">
                            <li>Emergency procedures</li>
                            <li>Containment methods</li>
                            <li>Decontamination protocols</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Specific Hazards Section -->
        <section class="content-section" style="background: rgba(231, 76, 60, 0.02);">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Specific Hazard Categories</h2>
                    <p class="section-subtitle">
                        Specialized training for specific hazards encountered in advanced laboratory work.
                    </p>
                </div>

                <div class="hazard-grid">
                    <div class="hazard-card">
                        <div class="hazard-icon">
                            <i class="fas fa-snowflake"></i>
                        </div>
                        <h3 class="card-title">Cryogenic Materials</h3>
                        <p class="card-description">
                            Safe handling and storage of extremely cold materials and liquids.
                        </p>
                        <ul class="card-features">
                            <li>Liquid nitrogen handling</li>
                            <li>Cryogenic PPE requirements</li>
                            <li>Storage and transport</li>
                            <li>Emergency procedures</li>
                        </ul>
                    </div>

                    <div class="hazard-card">
                        <div class="hazard-icon">
                            <i class="fas fa-compress-arrows-alt"></i>
                        </div>
                        <h3 class="card-title">High-Pressure Systems</h3>
                        <p class="card-description">
                            Safety protocols for high-pressure equipment and gas systems.
                        </p>
                        <ul class="card-features">
                            <li>Gas cylinder safety</li>
                            <li>Pressure vessel protocols</li>
                            <li>Maintenance requirements</li>
                            <li>Emergency shutdown procedures</li>
                        </ul>
                    </div>

                    <div class="hazard-card">
                        <div class="hazard-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="card-title">Laser Safety</h3>
                        <p class="card-description">
                            Comprehensive laser safety protocols and protection measures.
                        </p>
                        <ul class="card-features">
                            <li>Laser classifications</li>
                            <li>Eye protection requirements</li>
                            <li>Beam control measures</li>
                            <li>Alignment procedures</li>
                        </ul>
                    </div>

                    <div class="hazard-card">
                        <div class="hazard-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="card-title">High-Voltage Systems</h3>
                        <p class="card-description">
                            Electrical safety for high-voltage laboratory equipment.
                        </p>
                        <ul class="card-features">
                            <li>Electrical hazard identification</li>
                            <li>Lockout/tagout procedures</li>
                            <li>Personal protective equipment</li>
                            <li>Emergency response</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Assessment Section -->
        <section class="content-section">
            <div class="container">
                <div class="assessment-section">
                    <div class="card-icon" style="margin: 0 auto 1.5rem;">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h2 class="assessment-title">Complete Your Assessment</h2>
                    <p class="assessment-description">
                        Demonstrate your specialized safety knowledge through written hazard assessment, practical skills demonstration,
                        and emergency response simulation. All components must be completed successfully.
                    </p>
                    <button class="btn-assessment" onclick="startAssessment()">
                        <i class="fas fa-play"></i> Start Assessment
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <img src="../assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
                    <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
                </div>
                <div class="footer-col">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../research.html">Research Areas</a></li>
                        <li><a href="../publications.html">Publications</a></li>
                        <li><a href="../education.html">Education Programs</a></li>
                        <li><a href="../um6p.html">About UM6P</a></li>
                        <li><a href="../laboratory.html">Laboratory</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" aria-label="Email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </a>
                    </div>
                    <p class="contact-info">
                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        Phone: +212 (0) 5 25 07 3000
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../scripts.js"></script>
    <script>
        // Set current year
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Progress bar animation
        window.addEventListener('scroll', function() {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });

        // Assessment function
        function startAssessment() {
            alert('Assessment functionality would be implemented here. This would include written hazard assessment, practical skills demonstration, and emergency response simulation.');
        }
    </script>
</body>
</html>
