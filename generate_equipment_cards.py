#!/usr/bin/env python3
"""
Generate HTML equipment cards from the Excel data
"""

def categorize_equipment(equipment_name):
    """Categorize equipment based on name"""
    name_lower = equipment_name.lower()
    
    if any(word in name_lower for word in ['microscope', 'sem', 'meb', 'tem', 'afm', 'optical', 'observer', 'eclipse', 'olympus']):
        return 'microscopy'
    elif any(word in name_lower for word in ['furnace', 'four', 'oven', 'reactor', 'synthesis', 'cvd', 'pvd', 'extrudeuse', 'press', 'heat press', 'tube furnace', 'moufle']):
        return 'synthesis'
    elif any(word in name_lower for word in ['battery', 'batterie', 'cycleur', 'electrochemical', 'potentiostat', 'cycler', 'impedance', 'bipotentiostat']):
        return 'electrochemical'
    elif any(word in name_lower for word in ['dsc', 'tga', 'atg', 'thermal', 'calorimeter', 'temperature', 'thermique']):
        return 'thermal'
    elif any(word in name_lower for word in ['xrd', 'ftir', 'raman', 'spectro', 'diffract', 'uv-vis', 'nmr', 'bet', 'chromatography', 'fluorescence']):
        return 'characterization'
    else:
        return 'characterization'  # default category

def get_icon(category, equipment_name):
    """Get appropriate icon for equipment"""
    name_lower = equipment_name.lower()
    
    if 'microscope' in name_lower or 'meb' in name_lower:
        return 'fas fa-microscope'
    elif 'battery' in name_lower or 'cycleur' in name_lower:
        return 'fas fa-battery-full'
    elif 'furnace' in name_lower or 'four' in name_lower:
        return 'fas fa-fire'
    elif 'balance' in name_lower:
        return 'fas fa-weight'
    elif 'spectro' in name_lower or 'ftir' in name_lower:
        return 'fas fa-chart-line'
    elif 'etuve' in name_lower:
        return 'fas fa-thermometer-half'
    elif 'ph' in name_lower:
        return 'fas fa-vial'
    else:
        return 'fas fa-cog'

def generate_equipment_card(image_url, name, brand, model, supplier, location, category=None):
    """Generate HTML for a single equipment card"""
    if not category:
        category = categorize_equipment(name)
    
    icon = get_icon(category, name)
    
    # Clean up empty values
    brand = brand if brand and str(brand).strip() else "N/A"
    model = model if model and str(model).strip() else "N/A"
    supplier = supplier if supplier and str(supplier).strip() else brand
    location = location if location and str(location).strip() else "Laboratory"
    
    return f'''                    <div class="equipment-card" data-category="{category}">
                        <div class="equipment-image">
                            <img src="{image_url}" alt="{name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="placeholder-icon" style="display: none;">
                                <i class="{icon}"></i>
                                <span>{name}</span>
                            </div>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">{name}</h3>
                            <div class="equipment-info">
                                <div class="equipment-detail">
                                    <span class="label">Brand:</span>
                                    <span class="value">{brand}</span>
                                </div>
                                <div class="equipment-detail">
                                    <span class="label">Model:</span>
                                    <span class="value">{model}</span>
                                </div>
                                <div class="equipment-detail">
                                    <span class="label">Supplier:</span>
                                    <span class="value">{supplier}</span>
                                </div>
                            </div>
                            <div class="location-badge">
                                <i class="fas fa-map-marker-alt"></i>
                                {location}
                            </div>
                        </div>
                    </div>

'''

# Sample equipment data from your Excel file
equipment_data = [
    # Continue with more equipment from your list...
    ("https://www.monash.edu/__data/assets/image/0016/1611331/Malvern-Mastersizer-2000-particle-size-analyser.jpg", "Granulomètre Laser", "Malvern", "Mastersizer APA 2000", "Tecnilab-HTDS", "Bloc 1"),
    ("https://www.tainstruments.com/wp-content/uploads/HR30-1.jpg", "Rhéomètre", "TA Instruments", "HR-2", "Reacting/SOTEKLAB", "Bloc 1"),
    ("https://www.tainstruments.com/wp-content/uploads/1-5.jpg", "DSC", "TA Instruments", "Discovery", "Reacting/SOTEKLAB", "Bloc 2"),
    ("https://www.tainstruments.com/wp-content/uploads/the-powerful-discovery-tga.jpg", "ATG", "TA Instruments", "Discovery", "Reacting/SOTEKLAB", "Bloc 2"),
    ("https://img.medicalexpo.fr/images_me/photo-m2/70876-8231561.jpg", "Purificateur d'eau", "Merck Milipore", "MiliQ", "Professional labo", "Bloc 1"),
    ("https://cdn.shopify.com/s/files/1/1879/0393/products/R300i_1024x1024.jpg?v=**********", "Rotavapeur", "Buchi", "R-300", "Tecnilab-HTDS", "Bloc 1"),
    ("https://www.francebiotechnologies.fr/image-produit/55848/T2/005584803254.jpg", "Lyophilisateur", "Cryotec", "Cosmos", "Mastoral diffusion", "Bloc 2"),
    ("http://www.selectscience.net/images/products/9241_BWB-BIO---Flame-Photometer_1.jpg", "Photomètre à Flamme", "BWB", "XP", "SOTEKLAB", "Bloc 1"),
    ("https://www.hellopro.fr/images/produit-2/8/0/5/texturometre-shimadzu-ez-test-sx-lx-lxhs-5266508.jpg", "Machine de test mécanique 5KN", "Shimadzu", "EZ-LX", "SCOMEDICA", "Bloc 1"),
    ("https://img.directindustry.fr/images_di/photo-g/14711-4441445.jpg", "DMA", "Perkin Elmer", "DMA 8000", "Tecnilab-HTDS", "Bloc 1"),
]

if __name__ == "__main__":
    print("<!-- Generated Equipment Cards -->")
    for equipment in equipment_data:
        card_html = generate_equipment_card(*equipment)
        print(card_html)
